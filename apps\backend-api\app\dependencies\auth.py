"""
Centralized authentication dependencies with clear separation of concerns
"""
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Annotated

from app.database import get_db, set_tenant_context
from app.middleware import get_current_active_user, get_current_tenant_user
from app.models.user import User, TenantUser
from app.utils.permissions import Permission, check_permission


# Type aliases for cleaner code
DatabaseSession = Annotated[Session, Depends(get_db)]
CurrentUser = Annotated[User, Depends(get_current_active_user)]
CurrentTenantUser = Annotated[TenantUser, Depends(get_current_tenant_user)]


def get_tenant_scoped_db(
    db: DatabaseSession,
    tenant_user: CurrentTenantUser
) -> Session:
    """
    Get database session with tenant context set
    
    Use this for all tenant-scoped operations to ensure proper data isolation
    """
    set_tenant_context(db, str(tenant_user.tenant_id))
    return db


def require_permission(permission: Permission):
    """
    Dependency factory for permission-based access control
    
    Usage:
        @router.get("/admin-only")
        async def admin_endpoint(
            tenant_user: CurrentTenantUser,
            _: None = Depends(require_permission(Permission.ADMIN_ACCESS))
        ):
    """
    def check_user_permission(tenant_user: CurrentTenantUser) -> None:
        if not check_permission(tenant_user.role.permissions, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {permission.value} required"
            )
    
    return check_user_permission


def require_admin_access(tenant_user: CurrentTenantUser) -> TenantUser:
    """
    Dependency for admin-only endpoints
    
    Checks if user has admin permissions in their current tenant
    """
    if not check_permission(tenant_user.role.permissions, Permission.ADMIN_ACCESS):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return tenant_user


# Commonly used dependency combinations
TenantScopedDB = Annotated[Session, Depends(get_tenant_scoped_db)]
AdminUser = Annotated[TenantUser, Depends(require_admin_access)]


class AuthDependencies:
    """
    Centralized class for authentication dependencies
    
    Provides clear, reusable dependencies for different access levels
    """
    
    @staticmethod
    def tenant_user_with_db(
        db: DatabaseSession,
        tenant_user: CurrentTenantUser
    ) -> tuple[Session, TenantUser]:
        """Get tenant-scoped DB and user in one dependency"""
        set_tenant_context(db, str(tenant_user.tenant_id))
        return db, tenant_user
    
    @staticmethod
    def admin_user_with_db(
        db: DatabaseSession,
        admin_user: AdminUser
    ) -> tuple[Session, TenantUser]:
        """Get tenant-scoped DB and admin user in one dependency"""
        set_tenant_context(db, str(admin_user.tenant_id))
        return db, admin_user


# Convenience type aliases for the combined dependencies
TenantUserWithDB = Annotated[tuple[Session, TenantUser], Depends(AuthDependencies.tenant_user_with_db)]
AdminUserWithDB = Annotated[tuple[Session, TenantUser], Depends(AuthDependencies.admin_user_with_db)]
