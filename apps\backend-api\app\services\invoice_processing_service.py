"""
Invoice Processing Service - Hanterar fakturabehandlingsflödet
"""

import logging
import time
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from uuid import UUID

from app.models import Invoice, Session as ProcessingSession, SessionLog
from app.services.llm_provider import get_llm_service, LLMResponse
from app.services.prompt_service import get_prompt_service

logger = logging.getLogger(__name__)


class InvoiceProcessingService:
    """Service för att hantera fakturabehandlingsflödet"""
    
    def __init__(self, db: Session):
        self.db = db
        self.llm_service = get_llm_service()
        self.prompt_service = get_prompt_service()
    
    async def process_invoice(self, invoice_id: UUID, tenant_id: UUID) -> Dict[str, Any]:
        """Kör hela fakturabehandlingsflödet"""
        try:
            # Hämta faktura
            invoice = self.db.query(Invoice).filter(
                Invoice.id == invoice_id,
                Invoice.tenant_id == tenant_id
            ).first()
            
            if not invoice:
                raise ValueError(f"Invoice {invoice_id} not found")
            
            # Skapa eller hämta session
            session = self._get_or_create_session(invoice)
            
            # Kör flödet steg för steg
            result = await self._run_processing_flow(session, invoice)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing invoice {invoice_id}: {e}")
            raise
    
    def _get_or_create_session(self, invoice: Invoice) -> ProcessingSession:
        """Skapa eller hämta session för fakturan"""
        session = self.db.query(ProcessingSession).filter(
            ProcessingSession.invoice_id == invoice.id
        ).first()
        
        if not session:
            session = ProcessingSession(
                tenant_id=invoice.tenant_id,
                invoice_id=invoice.id,
                status="pending"
            )
            self.db.add(session)
            self.db.commit()
            self.db.refresh(session)
            logger.info(f"Created new session {session.id} for invoice {invoice.id}")
        
        return session
    
    async def _run_processing_flow(self, session: ProcessingSession, invoice: Invoice) -> Dict[str, Any]:
        """Kör hela bearbetningsflödet"""
        try:
            session.status = "processing"
            self.db.commit()
            
            # Steg 1: Extrahera
            await self._step_extrahera(session, invoice)
            
            # Steg 2: Kontext
            await self._step_kontext(session)
            
            # Steg 3: Hitta konto
            await self._step_hitta_konto(session)
            
            # Steg 4: Bokföra
            await self._step_bokfora(session, invoice)
            
            # Markera som klar
            session.status = "completed"
            session.current_step = None
            invoice.status = "completed"
            self.db.commit()
            
            return {
                "session_id": str(session.id),
                "status": "completed",
                "message": "Invoice processing completed successfully"
            }
            
        except Exception as e:
            session.status = "failed"
            session.error_message = str(e)
            session.failed_step = session.current_step
            invoice.status = "failed"
            invoice.processing_error = str(e)
            self.db.commit()
            
            logger.error(f"Processing failed for session {session.id}: {e}")
            raise
    
    async def _step_extrahera(self, session: ProcessingSession, invoice: Invoice):
        """Steg 1: Extrahera fakturainformation"""
        session.current_step = "extrahera"
        self.db.commit()
        
        start_time = time.time()
        
        try:
            # Formatera prompt
            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "extrahera",
                file_data=invoice.file_data
            )
            
            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            
            if not response.success:
                raise Exception(f"LLM error in extrahera step: {response.error}")
            
            # Spara resultat
            session.extracted_data = response.data
            session.extracted_reasoning = response.reasoning
            
            # Logga steget
            self._log_step(
                session=session,
                step_name="extrahera",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True
            )
            
            self.db.commit()
            logger.info(f"Completed extrahera step for session {session.id}")
            
        except Exception as e:
            self._log_step(
                session=session,
                step_name="extrahera",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e)
            )
            raise
    
    async def _step_kontext(self, session: ProcessingSession):
        """Steg 2: Skapa kontext"""
        session.current_step = "kontext"
        self.db.commit()
        
        start_time = time.time()
        
        try:
            # Formatera prompt
            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "kontext",
                extracted_data=session.extracted_data
            )
            
            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            
            if not response.success:
                raise Exception(f"LLM error in kontext step: {response.error}")
            
            # Spara resultat
            session.context_data = response.data
            session.context_reasoning = response.reasoning
            
            # Logga steget
            self._log_step(
                session=session,
                step_name="kontext",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True
            )
            
            self.db.commit()
            logger.info(f"Completed kontext step for session {session.id}")
            
        except Exception as e:
            self._log_step(
                session=session,
                step_name="kontext",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e)
            )
            raise
    
    async def _step_hitta_konto(self, session: ProcessingSession):
        """Steg 3: Hitta lämpliga konton"""
        session.current_step = "hitta_konto"
        self.db.commit()
        
        start_time = time.time()
        
        try:
            # Formatera prompt
            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "hitta_konto",
                context_data=session.context_data
            )
            
            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            
            if not response.success:
                raise Exception(f"LLM error in hitta_konto step: {response.error}")
            
            # Försök att parsa JSON data
            try:
                import json
                account_data = json.loads(response.data) if isinstance(response.data, str) else response.data
            except json.JSONDecodeError:
                # Om det inte är JSON, spara som text
                account_data = {"raw_response": response.data}
            
            # Spara resultat
            session.account_data = account_data
            session.account_reasoning = response.reasoning
            
            # Logga steget
            self._log_step(
                session=session,
                step_name="hitta_konto",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True
            )
            
            self.db.commit()
            logger.info(f"Completed hitta_konto step for session {session.id}")
            
        except Exception as e:
            self._log_step(
                session=session,
                step_name="hitta_konto",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e)
            )
            raise
    
    async def _step_bokfora(self, session: ProcessingSession, invoice: Invoice):
        """Steg 4: Bokföra (förbereda för ERP integration)"""
        session.current_step = "bokfora"
        self.db.commit()
        
        start_time = time.time()
        
        try:
            # Bestäm ERP system baserat på import_typ
            erp_system = "manual" if invoice.import_typ == "manuell" else invoice.import_typ
            
            # Formatera prompt
            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "bokfora",
                context_data=session.context_data,
                account_data=session.account_data,
                erp_system=erp_system
            )
            
            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            
            if not response.success:
                raise Exception(f"LLM error in bokfora step: {response.error}")
            
            # Försök att parsa JSON data
            try:
                import json
                booking_result = json.loads(response.data) if isinstance(response.data, str) else response.data
            except json.JSONDecodeError:
                booking_result = {"raw_response": response.data, "status": "needs_review"}
            
            # Spara resultat
            session.booking_result = booking_result
            session.booking_reasoning = response.reasoning
            
            # Logga steget
            self._log_step(
                session=session,
                step_name="bokfora",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True
            )
            
            self.db.commit()
            logger.info(f"Completed bokfora step for session {session.id}")
            
        except Exception as e:
            self._log_step(
                session=session,
                step_name="bokfora",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e)
            )
            raise
    
    def _log_step(self, session: ProcessingSession, step_name: str, prompt_sent: str, 
                  llm_response: str, reasoning: str, execution_time_ms: float, 
                  success: bool, error_message: str = None):
        """Logga ett bearbetningssteg"""
        log_entry = SessionLog(
            tenant_id=session.tenant_id,
            session_id=session.id,
            step_name=step_name,
            prompt_sent=prompt_sent,
            llm_response=llm_response,
            reasoning=reasoning,
            execution_time_ms=execution_time_ms,
            success=success,
            error_message=error_message
        )
        
        self.db.add(log_entry)
        # Commit sker i anropande metod
