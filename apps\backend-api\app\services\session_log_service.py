"""
Session Log Service - Hanterar loggning och visning av session data
"""

import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from uuid import UUID

from app.models import SessionLog, Session as ProcessingSession, Invoice

logger = logging.getLogger(__name__)


class SessionLogService:
    """Service för att hantera session loggning och visning"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_session_logs(self, session_id: UUID, tenant_id: UUID) -> List[SessionLog]:
        """Hämta alla loggar för en session"""
        # RLS hanterar tenant-filtrering automatiskt
        return self.db.query(SessionLog).filter(
            SessionLog.session_id == session_id
        ).order_by(SessionLog.created_at).all()
    
    def get_session_detail(self, session_id: UUID, tenant_id: UUID) -> Dict[str, Any]:
        """Hämta detaljerad information om en session"""
        logger.info(f"Getting session detail for session_id: {session_id}, tenant_id: {tenant_id}")

        # Hämta session - RLS hanterar tenant-filtrering automatiskt
        session = self.db.query(ProcessingSession).filter(
            ProcessingSession.id == session_id
        ).first()

        logger.info(f"Session found: {session is not None}")
        if not session:
            raise ValueError(f"Session {session_id} not found")

        # Hämta faktura - RLS hanterar tenant-filtrering automatiskt
        invoice = self.db.query(Invoice).filter(
            Invoice.id == session.invoice_id
        ).first()

        # Hämta loggar - RLS hanterar tenant-filtrering automatiskt
        logs = self.db.query(SessionLog).filter(
            SessionLog.session_id == session_id
        ).order_by(SessionLog.created_at).all()
        
        # Bygg detaljerad response
        return {
            "session": {
                "id": str(session.id),
                "invoice_id": str(session.invoice_id),
                "status": session.status,
                "current_step": session.current_step,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat(),
                "error_message": session.error_message,
                "failed_step": session.failed_step
            },
            "invoice": {
                "id": str(invoice.id),
                "import_typ": invoice.import_typ,
                "supplier_name": invoice.supplier_name,
                "original_filename": invoice.original_filename,
                "status": invoice.status
            } if invoice else None,
            "processing_results": {
                "extracted_data": session.extracted_data,
                "extracted_reasoning": session.extracted_reasoning,
                "context_data": session.context_data,
                "context_reasoning": session.context_reasoning,
                "account_data": session.account_data,
                "account_reasoning": session.account_reasoning,
                "booking_result": session.booking_result,
                "booking_reasoning": session.booking_reasoning
            },
            "logs": [
                {
                    "id": str(log.id),
                    "step_name": log.step_name,
                    "prompt_sent": log.prompt_sent,
                    "llm_response": log.llm_response,
                    "reasoning": log.reasoning,
                    "execution_time_ms": log.execution_time_ms,
                    "success": log.success,
                    "error_message": log.error_message,
                    "created_at": log.created_at.isoformat()
                }
                for log in logs
            ],
            "statistics": self._calculate_session_statistics(logs)
        }
    
    def get_sessions_summary(self, tenant_id: UUID, limit: int = 50, offset: int = 0,
                           status_filter: Optional[str] = None) -> Dict[str, Any]:
        """Hämta sammanfattning av sessions"""
        # RLS hanterar tenant-filtrering automatiskt
        query = self.db.query(ProcessingSession)

        if status_filter:
            query = query.filter(ProcessingSession.status == status_filter)

        # Hämta sessions med paginering
        sessions = query.order_by(ProcessingSession.created_at.desc()).offset(offset).limit(limit).all()

        # Räkna totalt antal
        total_count = query.count()

        # Bygg response
        sessions_data = []
        for session in sessions:
            # Hämta relaterad faktura - RLS hanterar tenant-filtrering automatiskt
            invoice = self.db.query(Invoice).filter(Invoice.id == session.invoice_id).first()

            # Räkna loggar för denna session - RLS hanterar tenant-filtrering automatiskt
            log_count = self.db.query(SessionLog).filter(
                SessionLog.session_id == session.id
            ).count()
            
            sessions_data.append({
                "id": str(session.id),
                "invoice_id": str(session.invoice_id),
                "status": session.status,
                "current_step": session.current_step,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat(),
                "error_message": session.error_message,
                "failed_step": session.failed_step,
                "invoice": {
                    "supplier_name": invoice.supplier_name if invoice else None,
                    "original_filename": invoice.original_filename if invoice else None,
                    "import_typ": invoice.import_typ if invoice else None
                },
                "log_count": log_count
            })
        
        return {
            "sessions": sessions_data,
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count
            },
            "statistics": self._calculate_tenant_statistics(tenant_id)
        }
    
    def _calculate_session_statistics(self, logs: List[SessionLog]) -> Dict[str, Any]:
        """Beräkna statistik för en session"""
        if not logs:
            return {
                "total_steps": 0,
                "successful_steps": 0,
                "failed_steps": 0,
                "total_execution_time_ms": 0,
                "average_execution_time_ms": 0,
                "steps_completed": []
            }
        
        successful_logs = [log for log in logs if log.success]
        failed_logs = [log for log in logs if not log.success]
        
        total_time = sum(log.execution_time_ms or 0 for log in logs)
        avg_time = total_time / len(logs) if logs else 0
        
        return {
            "total_steps": len(logs),
            "successful_steps": len(successful_logs),
            "failed_steps": len(failed_logs),
            "total_execution_time_ms": total_time,
            "average_execution_time_ms": avg_time,
            "steps_completed": [log.step_name for log in successful_logs]
        }
    
    def _calculate_tenant_statistics(self, tenant_id: UUID) -> Dict[str, Any]:
        """Beräkna statistik för en tenant"""
        # Räkna sessions per status
        status_counts = {}
        statuses = ["pending", "processing", "completed", "failed"]
        
        for status in statuses:
            # RLS hanterar tenant-filtrering automatiskt
            count = self.db.query(ProcessingSession).filter(
                ProcessingSession.status == status
            ).count()
            status_counts[status] = count

        # Räkna totalt antal loggar - RLS hanterar tenant-filtrering automatiskt
        total_logs = self.db.query(SessionLog).count()

        # Räkna framgångsrika vs misslyckade loggar - RLS hanterar tenant-filtrering automatiskt
        successful_logs = self.db.query(SessionLog).filter(
            SessionLog.success == True
        ).count()
        
        failed_logs = total_logs - successful_logs
        
        return {
            "sessions_by_status": status_counts,
            "total_sessions": sum(status_counts.values()),
            "total_logs": total_logs,
            "successful_logs": successful_logs,
            "failed_logs": failed_logs,
            "success_rate": (successful_logs / total_logs * 100) if total_logs > 0 else 0
        }
    
    def get_step_logs(self, session_id: UUID, step_name: str, tenant_id: UUID) -> List[SessionLog]:
        """Hämta loggar för ett specifikt steg"""
        # RLS hanterar tenant-filtrering automatiskt
        return self.db.query(SessionLog).filter(
            SessionLog.session_id == session_id,
            SessionLog.step_name == step_name
        ).order_by(SessionLog.created_at).all()

    def delete_session_logs(self, session_id: UUID, tenant_id: UUID) -> int:
        """Radera alla loggar för en session"""
        # RLS hanterar tenant-filtrering automatiskt
        deleted_count = self.db.query(SessionLog).filter(
            SessionLog.session_id == session_id
        ).delete()
        
        self.db.commit()
        logger.info(f"Deleted {deleted_count} logs for session {session_id}")
        return deleted_count
    
    def export_session_logs(self, session_id: UUID, tenant_id: UUID, format: str = "json") -> Dict[str, Any]:
        """Exportera session loggar i olika format"""
        session_detail = self.get_session_detail(session_id, tenant_id)
        
        if format.lower() == "json":
            return session_detail
        elif format.lower() == "csv":
            # Implementera CSV export om behövs
            raise NotImplementedError("CSV export not yet implemented")
        else:
            raise ValueError(f"Unsupported export format: {format}")


def get_session_log_service(db: Session) -> SessionLogService:
    """Factory function för att skapa SessionLogService"""
    return SessionLogService(db)
